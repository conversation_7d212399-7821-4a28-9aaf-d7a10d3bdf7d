<!--main content start-->
<!-- FIXED: Array initialization issue - <?php echo date('Y-m-d H:i:s'); ?> -->
<section id="main-content">
    <section class="wrapper site-min-height">
        <!-- page start-->
        <header class="panel-heading">
             <?php  echo lang('pharmacy'); ?> <?php echo lang('report'); ?>
        </header>
        <div class="col-md-12">
            <div class="col-md-7">  
                <section>
                    <form role="form" class="f_report" action="finance/pharmacy/financialReport" method="post" enctype="multipart/form-data">
                        <label class="range">Date Range</label> 
                        <div class="form-group">

                            <div class="col-md-6">
                                <div class="input-group input-large" data-date="13/07/2013" data-date-format="mm/dd/yyyy">
                                    <input type="text" class="form-control dpd1" name="date_from" value="<?php
                                    if (!empty($from)) {
                                        echo $from;
                                    }
                                    ?>" placeholder=" <?php echo lang('date_from'); ?> ">
                                    <span class="input-group-addon"> <?php echo lang('to'); ?> </span>
                                    <input type="text" class="form-control dpd2" name="date_to" value="<?php
                                    if (!empty($to)) {
                                        echo $to;
                                    }
                                    ?>" placeholder=" <?php echo lang('date_to'); ?> ">
                                </div>
                                <div class="row"></div>
                                <span class="help-block"></span> 
                            </div>
                            <div class="col-md-6">
                                <button type="submit" name="submit" class="btn btn-info range_submit"> <?php echo lang('submit'); ?> </button>
                            </div>
                        </div>
                    </form>
                </section>
                <section class="">
                    <div class="col-md-3 panel-body">
                        <label class="">Date From</label> 
                        <div class="paanel"><?php
                            if (!empty($from)) {
                                echo $from;
                            }
                            ?>
                        </div>
                    </div>
                    <div class="col-md-3 panel-body">
                        <label class="">Date To</label> 
                        <div class="paanel"> <?php
                            if (!empty($to)) {
                                echo $to;
                            }
                            ?>
                        </div>
                    </div>
                </section>
            </div>
            <div class="col-md-5">
            </div>
        </div>

        <?php
        // Initialize ALL arrays at the very beginning to prevent null errors
        $category_id_for_report = array();
        $total_cost_by_category = array();
        $total_payment_by_category = array();
        $total_expense_by_category = array();

        if (!empty($payments)) {
            $paid_number = 0;
            foreach ($payments as $payment) {
                $paid_number = $paid_number + 1;
            }
        }
        ?>
        <div class="row">
            <div class="col-lg-7">

                <section class="panel">
                    <header class="panel-heading">
                        <i class="fa fa-money"></i>  <?php echo lang('sales'); ?> <?php echo lang('report'); ?>
                    </header>
                    <table class="table table-striped table-advance table-hover">
                        <thead>
                            <tr>
                                <th><?php echo lang('item_name'); ?> </th>
                                <th><?php echo lang('quantity'); ?> </th>
                                <th><?php echo lang('total'); ?> <?php echo lang('purchase'); ?> <?php echo lang('cost'); ?> </th>
                                <th class="hidden-phone"><?php echo lang('total'); ?> <?php echo lang('sale'); ?> <?php echo lang('cost'); ?> </th>

                            </tr>
                        </thead>
                        <tbody>

                            <?php
                            // Array already initialized at the beginning of the file

                            // Check if medicines and payments exist before processing
                            if (!empty($medicines) && !empty($payments)) {
                                foreach ($medicines as $medicine_name) {
                                    foreach ($payments as $payment) {
                                        // Check if category_name exists and is not empty
                                        if (!empty($payment->category_name)) {
                                            $categories_in_payment = explode(',', $payment->category_name);
                                            foreach ($categories_in_payment as $category_in_payment) {
                                                $category_id = explode('*', $category_in_payment);
                                                if (isset($category_id[0]) && $category_id[0] == $medicine_name->id) {
                                                    $category_id_for_report[] = $category_id[0];
                                                }
                                            }
                                        }
                                    }
                                }
                            }

                            // DEBUG: Check variable state before array_unique
                            echo "<!-- DEBUG: category_id_for_report is " . (isset($category_id_for_report) ? 'SET' : 'NOT SET') . " -->";
                            if (isset($category_id_for_report)) {
                                echo "<!-- DEBUG: category_id_for_report type: " . gettype($category_id_for_report) . " -->";
                                echo "<!-- DEBUG: category_id_for_report count: " . (is_array($category_id_for_report) ? count($category_id_for_report) : 'NOT ARRAY') . " -->";
                            }

                            // Apply array_unique only if we have data - SAFE CHECK
                            $category_id_for_reports = (isset($category_id_for_report) && !empty($category_id_for_report)) ? array_unique($category_id_for_report) : array();
                            ?>






                            <?php
                            foreach ($medicines as $category) {

                                if (in_array($category->id, $category_id_for_reports)) {
                                    ?>
                                    <tr class="">
                                        <td><?php echo $category->name ?></td>
                                        <?php
                                        // Initialize arrays to prevent null errors
                                        $amount_per_category = array();
                                        $cost_per_category = array();
                                        $quantity = array();

                                        foreach ($payments as $payment) {
                                            $category_names_and_amounts = $payment->category_name;
                                            $category_names_and_amounts = explode(',', $category_names_and_amounts);
                                            foreach ($category_names_and_amounts as $category_name_and_amount) {
                                                $category_name = explode('*', $category_name_and_amount);
                                                if (($category->id == $category_name[0])) {
                                                    $amount_per_category[] = $category_name[1] * $category_name[2];
                                                    $cost_per_category[] = $category_name[2] * $category_name[3];
                                                    $quantity[] = $category_name[2];
                                                }
                                            }
                                        }
                                        ?>
                                        <td>
                                            <?php
                                            if (!empty($quantity)) {
                                                echo array_sum($quantity);
                                                $quantity[] = array_sum($quantity);
                                            } else {
                                                echo '0';
                                            }

                                            $quantity = NULL;
                                            ?>
                                        </td>
                                        <td>
                                            <?php echo $settings->currency; ?> 
                                            <?php
                                            if (!empty($cost_per_category)) {
                                                echo array_sum($cost_per_category);
                                                $total_cost_by_category[] = array_sum($cost_per_category);
                                            } else {
                                                echo '0';
                                            }

                                            $cost_per_category = NULL;
                                            ?>
                                        </td>
                                        <td>
                                            <?php echo $settings->currency; ?> 
                                            <?php
                                            if (!empty($amount_per_category)) {
                                                echo array_sum($amount_per_category);
                                                $total_payment_by_category[] = array_sum($amount_per_category);
                                            } else {
                                                echo '0';
                                            }

                                            $amount_per_category = NULL;
                                            ?>
                                        </td>
                                    </tr>
                                    <?php
                                }
                            }
                            ?>

                        </tbody>
                        <tbody>
                            <tr>
                                <td><h3> <?php echo lang('sub_total'); ?>  </h3></td>
                                <td></td>
                                <td>

                                    <?php echo $settings->currency; ?>
                                    <?php
                                    if (!empty($total_cost_by_category)) {
                                        echo array_sum($total_cost_by_category);
                                    } else {
                                        echo '0';
                                    }
                                    ?> 
                                </td>
                                <td>
                                    <?php echo $settings->currency; ?>
                                    <?php
                                    if (!empty($total_payment_by_category)) {
                                        echo array_sum($total_payment_by_category);
                                    } else {
                                        echo '0';
                                    }
                                    ?> 
                                </td>
                            </tr>

                            <tr>
                                <td><h5> <?php echo lang('total'); ?>   <?php echo lang('discount'); ?> </h5></td>
                                <td></td>
                                <td></td>
                                <td>
                                    <?php echo $settings->currency; ?>
                                    <?php
                                    // Initialize discount array to prevent null errors
                                    $discount = array();
                                    if (!empty($payments)) {
                                        foreach ($payments as $payment) {
                                            $discount[] = $payment->flat_discount;
                                        }
                                        if ($paid_number > 0) {
                                            echo array_sum($discount);
                                        } else {
                                            echo '0';
                                        }
                                    } else {
                                        echo '0';
                                    }
                                    ?>
                                </td>
                            </tr>
                            <!--
                            <tr>
                                <td><h5> <?php echo lang('total'); ?>   <?php echo lang('vat'); ?> </h5></td>
                                <td></td>
                                <td></td>
                                <td>
                                    <?php echo $settings->currency; ?>
                                    <?php
                                    // Initialize vat array to prevent null errors
                                    $vat = array();
                                    if (!empty($payments)) {
                                        foreach ($payments as $payment) {
                                            $vat[] = $payment->flat_vat;
                                        }
                                        if ($paid_number > 0) {
                                            echo array_sum($vat);
                                        } else {
                                            echo '0';
                                        }
                                    } else {
                                        echo '0';
                                    }
                                    ?>
                                </td>
                            </tr>
                            -->
                            <tr>
                                <td><h5><?php echo lang('gross'); ?> <?php echo lang('sales'); ?> </h5></td>
                                <td></td>
                                <td></td>
                                <td>
                                    <?php echo $settings->currency; ?>
                                    <?php
                                    if (!empty($payments)) {
                                        if ($paid_number > 0) {
                                            // Ensure arrays are initialized before using array_sum
                                            $total_payment_sum = !empty($total_payment_by_category) ? array_sum($total_payment_by_category) : 0;
                                            $discount_sum = !empty($discount) ? array_sum($discount) : 0;
                                            $vat_sum = !empty($vat) ? array_sum($vat) : 0;
                                            $gross = $total_payment_sum - $discount_sum + $vat_sum;
                                            echo $gross;
                                        } else {
                                            echo '0';
                                        }
                                    } else {
                                        echo '0';
                                    }
                                    ?>
                                </td>
                            </tr>
                            <!--
                            <tr>
                                <td><h5> <?php echo lang('due_amount'); ?>  </h5></td>
                                <td></td>
                                <td></td>
                                <td>
                                    <?php echo $settings->currency; ?>
                                    <?php
                                    // Initialize amount_received array to prevent null errors
                                    $amount_received = array();
                                    if (!empty($payments)) {
                                        foreach ($payments as $payment) {
                                            $amount_received[] = $payment->amount_received;
                                        }
                                        if ($paid_number > 0) {
                                            $amount_received = array_sum($amount_received);
                                            echo $gross - $amount_received;
                                        } else {
                                            echo '0';
                                        }
                                    } else {
                                        echo '0';
                                    }
                                    ?>
                                </td>
                            </tr>
                            -->
                        </tbody>
                    </table>
                </section>
                <section></section>
                <section class="panel">
                    <header class="panel-heading">
                        <i class="fa fa-money"></i>   <?php echo lang('expense'); ?>   <?php echo lang('report'); ?> 
                    </header>
                    <table class="table table-striped table-advance table-hover">
                        <thead>
                            <tr>
                                <th><?php echo lang('category'); ?> </th>
                                <th class="hidden-phone"><?php echo lang('amount'); ?> </th>

                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($expense_categories as $category) { ?>
                                <tr class="">
                                    <td><?php echo $category->category ?></td>
                                    <td>
                                        <?php echo $settings->currency; ?>
                                        <?php
                                        // Initialize amount_per_category array for expenses
                                        $amount_per_category = array();
                                        foreach ($expenses as $expense) {
                                            $category_name = $expense->category;


                                            if (($category->category == $category_name)) {
                                                $amount_per_category[] = $expense->amount;
                                            }
                                        }
                                        if (!empty($amount_per_category)) {
                                            $total_expense_by_category[] = array_sum($amount_per_category);
                                            echo array_sum($amount_per_category);
                                        } else {
                                            echo '0';
                                        }

                                        $amount_per_category = NULL;
                                        ?>
                                    </td>
                                </tr>
                            <?php } ?>

                        </tbody>
                    </table>
                </section>
            </div>

            <div class="col-lg-5">

                <section class="panel">
                    <div class="weather-bg">
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-xs-4">
                                    <i class="fa fa-money"></i>
                                    <?php echo lang('gross'); ?> <?php echo lang('p_price'); ?> 
                                </div>
                                <div class="col-xs-8">
                                    <div class="degree">
                                        <?php echo $settings->currency; ?>
                                        <?php
                                        if (!empty($payments)) {
                                            if (($paid_number > 0)) {
                                                if (!empty($total_cost_by_category)) {
                                                    $total_cost = array_sum($total_cost_by_category);
                                                    echo number_format($total_cost, 2, '.', ',');
                                                } else {
                                                    $total_cost = 0;
                                                    echo number_format($total_cost, 2, '.', ',');
                                                }
                                            }
                                        } else {
                                            echo '0';
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <section class="panel">
                    <div class="weather-bg">
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-xs-4">
                                    <i class="fa fa-money"></i>
                                    <?php echo lang('gross'); ?> <?php echo lang('s_price'); ?> 
                                </div>
                                <div class="col-xs-8">
                                    <div class="degree">
                                        <?php echo $settings->currency; ?>
                                        <?php
                                        if (!empty($payments)) {
                                            if (($paid_number > 0)) {
                                                if (!empty($gross)) {

                                                    echo number_format($gross, 2, '.', ',');
                                                } else {
                                                    $gross = 0;
                                                    echo number_format($gross, 2, '.', ',');
                                                }
                                            }
                                        } else {
                                            echo '0';
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <section class="panel">
                    <div class="weather-bg">
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-xs-4">
                                    <i class="fa fa-money"></i>
                                    <?php echo lang('gross_expense'); ?> 
                                </div>
                                <div class="col-xs-8">
                                    <div class="degree">
                                        <?php echo $settings->currency; ?>
                                        <?php
                                        if (!empty($total_expense_by_category)) {
                                            $total_expense = array_sum($total_expense_by_category);
                                            echo number_format($total_expense, 2, '.', ',');
                                        } else {
                                            $total_expense = 0;
                                            echo number_format($total_expense, 2, '.', ',');
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>


                <section class="panel">
                    <div class="weather-bg">
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-xs-4">
                                    <i class="fa fa-money"></i>
                                    <?php echo lang('profit'); ?> 
                                </div>
                                <div class="col-xs-8">
                                    <div class="degree">
                                        <?php echo $settings->currency; ?>
                                        <?php
                                        $profit = $gross - $total_cost - $total_expense;
                                        echo number_format($profit, 2, '.', ',');
                                        ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>


            </div>
        </div>
        <!-- page end-->
    </section>
</section>
<!--main content end-->
<!--footer start-->
